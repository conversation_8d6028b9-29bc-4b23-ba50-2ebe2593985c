import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ElementRef, Renderer2, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Playlist } from '../../../models/playlist.model';

export interface Schedule {
  id: string;
  startTime: string;
  endTime: string;
  playlistName: string;
  duration: number;
  repeat: 'once' | 'daily' | 'weekly';
  status: 'active' | 'inactive' | 'completed';
  daysOfWeek?: number[]; // 0-6, Sunday to Saturday
}

interface CalendarEvent {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  status: string;
  repeat: string;
  daysOfWeek?: number[];
  duration: number;
  playlistName: string;
}

@Component({
  selector: 'app-schedule-calendar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './schedule-calendar.component.html',
  styleUrls: ['./schedule-calendar.component.scss']
})
export class ScheduleCalendarComponent implements OnInit, OnChanges {
  @Input() schedules: Schedule[] = [];
  @Input() selectedScreenName: string | null = null;
  @Input() disableEventClick: boolean = false; // New input to disable event click
  @Output() addSchedule = new EventEmitter<void>();
  @Output() deleteSchedule = new EventEmitter<Schedule>();
  @Output() scheduleUpdated = new EventEmitter<Schedule>();
  
  // New output for handling playlist drops
  @Output() playlistDropped = new EventEmitter<{ playlist: Playlist, timeSlot: { hour: number, minute: number } }>();

  calendarEvents: CalendarEvent[] = [];

  // View mode and navigation
  viewMode: 'day' | 'week' = 'day';
  currentWeekStart = new Date();

  // Days: 0 = Sunday, 1 = Monday, etc.
  currentDay = new Date().getDay();
  dayOptions = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  hours = Array.from({ length: 24 }, (_, i) => i);
  currentTimePosition = -1;

  // Resizing properties
  isResizing = false;
  resizingEvent: CalendarEvent | null = null;
  resizeDirection: 'top' | 'bottom' | null = null;
  initialY = 0;
  initialTop = 0;
  initialHeight = 0;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    this.updateCurrentTimePosition();
    this.initializeWeekStart();

    // Update time position every minute
    setInterval(() => {
      this.updateCurrentTimePosition();
    }, 60000);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['schedules'] && changes['schedules'].currentValue) {
      // Use setTimeout to ensure the change detection cycle completes before generating events
      setTimeout(() => {
        this.generateCalendarEvents();
      }, 0);
    }
  }

  private generateCalendarEvents() {
    this.calendarEvents = [];

    if (!this.schedules) return;

    // Group schedules by time slot (start_time and end_time)
    const timeSlotMap = new Map<string, CalendarEvent[]>();
    
    this.schedules.forEach(schedule => {
      // Create a unique key for each time slot
      const timeKey = `${schedule.startTime}-${schedule.endTime}`;
      
      if (!timeSlotMap.has(timeKey)) {
        timeSlotMap.set(timeKey, []);
      }
      
      timeSlotMap.get(timeKey)!.push({
        id: schedule.id,
        title: schedule.playlistName,
        startTime: schedule.startTime,
        endTime: schedule.endTime,
        status: schedule.status,
        repeat: schedule.repeat,
        daysOfWeek: schedule.daysOfWeek,
        duration: schedule.duration,
        playlistName: schedule.playlistName
      });
    });
    
    // For each time slot, if there are multiple events, merge them or handle overlap
    timeSlotMap.forEach((events, timeKey) => {
      if (events.length === 1) {
        // Single event, add it directly
        this.calendarEvents.push(events[0]);
      } else {
        // Multiple events with the same time, create a combined event
        const firstEvent = events[0];
        const combinedDays = [...new Set(events.flatMap(event => event.daysOfWeek || []))];
        
        // Create a combined event with all playlist names
        const combinedTitles = [...new Set(events.map(event => event.title))].join(', ');
        
        const combinedEvent: CalendarEvent = {
          id: `combined-${timeKey}`,
          title: combinedTitles,
          startTime: firstEvent.startTime,
          endTime: firstEvent.endTime,
          status: 'active', // Combined events are always active
          repeat: 'once',
          daysOfWeek: combinedDays,
          duration: firstEvent.duration,
          playlistName: combinedTitles
        };
        
        this.calendarEvents.push(combinedEvent);
      }
    });
  }

  // Check if two time ranges overlap
  private timeRangesOverlap(start1: string, end1: string, start2: string, end2: string): boolean {
    const start1Minutes = this.timeToMinutes(start1);
    const end1Minutes = this.timeToMinutes(end1);
    const start2Minutes = this.timeToMinutes(start2);
    const end2Minutes = this.timeToMinutes(end2);
    
    // Handle overnight events
    const isOvernight1 = end1Minutes < start1Minutes;
    const isOvernight2 = end2Minutes < start2Minutes;
    
    // If both are overnight, they always overlap
    if (isOvernight1 && isOvernight2) return true;
    
    // If one is overnight, check overlap
    if (isOvernight1) {
      return start2Minutes >= start1Minutes || end2Minutes <= end1Minutes;
    }
    
    if (isOvernight2) {
      return start1Minutes >= start2Minutes || end1Minutes <= end2Minutes;
    }
    
    // Both are regular events
    return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
  }

  // Convert time string (HH:MM) to minutes since midnight
  private timeToMinutes(time: string): number {
    if (!time) return 0;
    const parts = time.split(':');
    if (parts.length < 2) return 0;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) return 0;

    return hours * 60 + minutes;
  }

  selectDay(day: number) {
    this.currentDay = day;
  }

  getEventsForSelectedDay(): CalendarEvent[] {
    return this.calendarEvents.filter(event => {
      // If daysOfWeek is not defined or is an empty array, don't show the event
      if (!event.daysOfWeek || event.daysOfWeek.length === 0) {
        return false;
      }
      // Show the event if it's scheduled for the current day
      return event.daysOfWeek.includes(this.currentDay);
    });
  }

  getEventsWithoutDays(): CalendarEvent[] {
    return this.calendarEvents.filter(event => 
      !event.daysOfWeek || event.daysOfWeek.length === 0
    );
  }

  onEventClick(event: CalendarEvent) {
    // Event click is disabled, do nothing
  }

  onDeleteClick(event: CalendarEvent, $event: Event) {
    $event.stopPropagation();
    // Convert CalendarEvent to Schedule for output
    const schedule: Schedule = {
      id: event.id,
      startTime: event.startTime,
      endTime: event.endTime,
      playlistName: event.playlistName,
      duration: event.duration,
      repeat: event.repeat as 'once' | 'daily' | 'weekly',
      status: event.status as 'active' | 'inactive' | 'completed',
      daysOfWeek: event.daysOfWeek
    };
    this.deleteSchedule.emit(schedule);
  }

  // Day editing functionality
  editingEventId: string | null = null;
  editedDays: number[] = [];

  startEditingDays(event: CalendarEvent, $event: Event) {
    $event.stopPropagation();
    this.editingEventId = event.id;
    this.editedDays = [...(event.daysOfWeek || [])];
  }

  onDaySelectionChange(day: number, event: Event) {
    const input = event.target as HTMLInputElement;
    const isChecked = input.checked;

    if (isChecked) {
      if (!this.editedDays.includes(day)) {
        this.editedDays.push(day);
      }
    } else {
      this.editedDays = this.editedDays.filter(d => d !== day);
    }
  }

  saveDays(event: CalendarEvent) {
    const updatedSchedule: Schedule = {
      ...event,
      daysOfWeek: [...this.editedDays].sort((a, b) => a - b),
      repeat: event.repeat as 'once' | 'daily' | 'weekly',
      status: event.status as 'active' | 'inactive' | 'completed',
    };
    this.scheduleUpdated.emit(updatedSchedule);
    this.cancelEditing();
  }

  cancelEditing() {
    this.editingEventId = null;
    this.editedDays = [];
  }

  getDayName(index: number): string {
    if (index >= 0 && index < this.dayOptions.length) {
      return this.dayOptions[index];
    }
    return '';
  }

  getEventTopPosition(startTime: string): number {
    if (!startTime) return 0;
    const parts = startTime.split(':');
    if (parts.length < 2) return 0;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) return 0;

    const totalMinutes = hours * 60 + minutes;
    return totalMinutes;
  }

  getEventTopPositionPercent(startTime: string): number {
    if (!startTime) return 0;
    const parts = startTime.split(':');
    if (parts.length < 2) return 0;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) return 0;

    const totalMinutes = hours * 60 + minutes;
    return (totalMinutes / 1440) * 100; // 1440 minutes in 24 hours
  }

  getEventHeight(startTime: string, endTime: string): number {
    if (!startTime || !endTime) return 0;

    const startParts = startTime.split(':');
    const endParts = endTime.split(':');

    if (startParts.length < 2 || endParts.length < 2) return 0;

    const startHours = parseInt(startParts[0], 10);
    const startMinutes = parseInt(startParts[1], 10);
    const endHours = parseInt(endParts[0], 10);
    const endMinutes = parseInt(endParts[1], 10);

    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) return 0;

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    // Handle overnight events
    const durationMinutes = endTotalMinutes > startTotalMinutes
      ? endTotalMinutes - startTotalMinutes
      : (24 * 60 - startTotalMinutes) + endTotalMinutes;

    return Math.max(durationMinutes, 20); // Minimum height of 20px
  }

  getEventHeightPercent(startTime: string, endTime: string): number {
    if (!startTime || !endTime) return 0;

    const startParts = startTime.split(':');
    const endParts = endTime.split(':');

    if (startParts.length < 2 || endParts.length < 2) return 0;

    const startHours = parseInt(startParts[0], 10);
    const startMinutes = parseInt(startParts[1], 10);
    const endHours = parseInt(endParts[0], 10);
    const endMinutes = parseInt(endParts[1], 10);

    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) return 0;

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    // Handle overnight events
    const durationMinutes = endTotalMinutes > startTotalMinutes
      ? endTotalMinutes - startTotalMinutes
      : (24 * 60 - startTotalMinutes) + endTotalMinutes;

    // Convert to percentage (1440 minutes in 24 hours)
    // Increase minimum height to ensure resize handles are visible
    return Math.max((durationMinutes / 1440) * 100, 5); // Minimum height of 5% for better handle visibility
  }

  formatTime(time: string): string {
    if (!time) return '';
    try {
      const parts = time.split(':');
      if (parts.length < 2) return time;

      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);

      if (isNaN(hours) || isNaN(minutes)) return time;

      const date = new Date();
      date.setHours(hours, minutes);
      return date.toLocaleTimeString([], {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return time;
    }
  }

  formatHour(hour: number): string {
    const date = new Date();
    date.setHours(hour, 0);
    return date.toLocaleTimeString([], { hour: '2-digit', hour12: false });
  }

  getEventColor(status: string): string {
    switch (status) {
      case 'active': return '#3b82f6'; // blue-500
      case 'inactive': return '#6b7280'; // gray-500
      case 'completed': return '#10b981'; // green-500
      default: return '#6b7280'; // gray-500
    }
  }

  updateCurrentTimePosition() {
    const now = new Date();
    const minutes = now.getHours() * 60 + now.getMinutes();
    this.currentTimePosition = minutes;
  }

  // New enhanced methods

  initializeWeekStart() {
    const today = new Date();
    const dayOfWeek = today.getDay();
    this.currentWeekStart = new Date(today);
    this.currentWeekStart.setDate(today.getDate() - dayOfWeek);
  }

  setViewMode(mode: 'day' | 'week') {
    this.viewMode = mode;
  }

  navigateWeek(direction: number) {
    this.currentWeekStart.setDate(this.currentWeekStart.getDate() + (direction * 7));
    this.currentWeekStart = new Date(this.currentWeekStart); // Trigger change detection
  }

  goToToday() {
    const today = new Date();
    this.currentDay = today.getDay();
    this.initializeWeekStart();
  }

  getDayDate(dayIndex: number): string {
    const date = new Date(this.currentWeekStart);
    date.setDate(this.currentWeekStart.getDate() + dayIndex);
    return date.getDate().toString();
  }

  getCurrentDateString(): string {
    const date = new Date(this.currentWeekStart);
    date.setDate(this.currentWeekStart.getDate() + this.currentDay);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getCurrentTimeString(): string {
    return new Date().toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  onTimeSlotClick(_hour: number) {
    // Emit add schedule - parent component can handle pre-filling time
    this.addSchedule.emit();
  }

  trackByEventId(_index: number, event: CalendarEvent): string {
    return event.id;
  }

  getStatusBadgeColor(status: string): string {
    switch (status) {
      case 'active': return '#10b981'; // emerald-500
      case 'inactive': return '#6b7280'; // gray-500
      case 'completed': return '#3b82f6'; // blue-500
      default: return '#6b7280'; // gray-500
    }
  }

  getDurationText(startTime: string, endTime: string): string {
    const startParts = startTime.split(':');
    const endParts = endTime.split(':');

    if (startParts.length < 2 || endParts.length < 2) return '';

    const startHours = parseInt(startParts[0], 10);
    const startMinutes = parseInt(startParts[1], 10);
    const endHours = parseInt(endParts[0], 10);
    const endMinutes = parseInt(endParts[1], 10);

    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) return '';

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    const durationMinutes = endTotalMinutes > startTotalMinutes
      ? endTotalMinutes - startTotalMinutes
      : (24 * 60 - startTotalMinutes) + endTotalMinutes;

    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    if (hours > 0) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  }

  getDaysOfWeekText(daysOfWeek: number[]): string {
    if (!daysOfWeek || daysOfWeek.length === 0) return '';
    
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    // Filter out invalid day indices
    const validDays = daysOfWeek.filter(day => day >= 0 && day < 7);
    return validDays.map(day => dayNames[day]).join(', ');
  }

  // Resizing logic
  onResizeStart(event: MouseEvent, calendarEvent: CalendarEvent, direction: 'top' | 'bottom') {
    event.preventDefault();
    event.stopPropagation();
    this.isResizing = true;
    this.resizingEvent = calendarEvent;
    this.resizeDirection = direction;
    this.initialY = event.clientY;

    const eventElement = this.el.nativeElement.querySelector(`[data-event-id='${calendarEvent.id}']`);
    if (eventElement) {
      this.initialTop = eventElement.offsetTop;
      this.initialHeight = eventElement.offsetHeight;
    }
    
    // Add visual feedback during resizing
    const handle = event.target as HTMLElement;
    if (handle) {
      handle.style.backgroundColor = 'rgba(37, 99, 235, 0.7)';
    }
    
    // Prevent text selection during resizing
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
  }

  @HostListener('document:mousemove', ['$event'])
  onResizing(event: MouseEvent) {
    if (!this.isResizing || !this.resizingEvent) return;

    const calendarGrid = this.el.nativeElement.querySelector('.relative.h-full');
    if (!calendarGrid) return;

    const gridRect = calendarGrid.getBoundingClientRect();
    const offsetY = event.clientY - gridRect.top;
    
    const totalMinutes = (offsetY / gridRect.height) * 1440;
    const snappedMinutes = Math.round(totalMinutes / 15) * 15;
    const newTime = this.minutesToTime(snappedMinutes);

    const eventElement = this.el.nativeElement.querySelector(`[data-event-id='${this.resizingEvent.id}']`);
    if (!eventElement) return;

    if (this.resizeDirection === 'top') {
      const endTime = this.resizingEvent.endTime;
      const endTimeMinutes = this.timeToMinutes(endTime);

      if (snappedMinutes < endTimeMinutes - 15) {
        this.resizingEvent.startTime = newTime;
        
        const newTop = this.getEventTopPositionPercent(this.resizingEvent.startTime);
        const newHeight = this.getEventHeightPercent(this.resizingEvent.startTime, endTime);
        
        this.renderer.setStyle(eventElement, 'top', `${newTop}%`);
        this.renderer.setStyle(eventElement, 'height', `${newHeight}%`);
      }
    } else if (this.resizeDirection === 'bottom') {
      const startTime = this.resizingEvent.startTime;
      const startTimeMinutes = this.timeToMinutes(startTime);

      if (snappedMinutes > startTimeMinutes + 15 && snappedMinutes <= 1439) {
        this.resizingEvent.endTime = newTime;
        
        const newHeight = this.getEventHeightPercent(startTime, this.resizingEvent.endTime);
        this.renderer.setStyle(eventElement, 'height', `${newHeight}%`);
      }
    }
    
    const timeElement = eventElement.querySelector('.event-time');
    if (timeElement) {
      this.renderer.setProperty(timeElement, 'textContent', `${this.formatTime(this.resizingEvent.startTime)} - ${this.formatTime(this.resizingEvent.endTime)}`);
    }
  }

  @HostListener('document:mouseup', ['$event'])
  onResizeEnd(event: MouseEvent) {
    // Reset visual feedback
    const handles = this.el.nativeElement.querySelectorAll('.resize-handle-top, .resize-handle-bottom');
    handles.forEach((handle: HTMLElement) => {
      handle.style.backgroundColor = '';
    });

    // Re-enable text selection
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';

    if (this.isResizing && this.resizingEvent) {
      const schedule: Schedule = {
        id: this.resizingEvent.id,
        startTime: this.resizingEvent.startTime,
        endTime: this.resizingEvent.endTime,
        playlistName: this.resizingEvent.playlistName,
        duration: this.resizingEvent.duration,
        repeat: this.resizingEvent.repeat as 'once' | 'daily' | 'weekly',
        status: this.resizingEvent.status as 'active' | 'inactive' | 'completed',
        daysOfWeek: this.resizingEvent.daysOfWeek
      };
      this.scheduleUpdated.emit(schedule);
    }

    this.isResizing = false;
    this.resizingEvent = null;
    this.resizeDirection = null;
    
    // Reset handle visibility after resizing
    setTimeout(() => {
      const allHandles = this.el.nativeElement.querySelectorAll('.resize-handle-top, .resize-handle-bottom');
      allHandles.forEach((handle: HTMLElement) => {
        handle.style.opacity = '';
      });
    }, 100);
  }

  private minutesToTime(minutes: number): string {
    const h = Math.floor(minutes / 60);
    const m = minutes % 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  }

  // Drag and drop methods for handling playlist drops
  onCalendarDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }
    
    // Add visual feedback to the calendar container
    const calendarContainer = this.el.nativeElement.querySelector('.calendar-container');
    if (calendarContainer) {
      calendarContainer.classList.add('drag-over');
    }
  }

  onCalendarDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    
    // Remove visual feedback from the calendar container
    const calendarContainer = this.el.nativeElement.querySelector('.calendar-container');
    if (calendarContainer) {
      calendarContainer.classList.remove('drag-over');
    }
  }

  onCalendarDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    
    // Remove visual feedback from the calendar container
    const calendarContainer = this.el.nativeElement.querySelector('.calendar-container');
    if (calendarContainer) {
      calendarContainer.classList.remove('drag-over');
    }
    
    if (event.dataTransfer) {
      try {
        const playlistData = event.dataTransfer.getData('text/plain');
        if (playlistData) {
          const playlist: Playlist = JSON.parse(playlistData);
          
          // Get the position where the playlist was dropped
          const calendarGrid = this.el.nativeElement.querySelector('.relative.h-full');
          if (calendarGrid) {
            const gridRect = calendarGrid.getBoundingClientRect();
            const offsetY = event.clientY - gridRect.top;
            
            // Calculate the time slot (hour and minute) based on the drop position
            const totalMinutes = (offsetY / gridRect.height) * 1440;
            const snappedMinutes = Math.round(totalMinutes / 15) * 15; // Snap to 15-minute intervals
            const hour = Math.floor(snappedMinutes / 60);
            const minute = snappedMinutes % 60;
            
            // Emit the playlist dropped event with the time slot information
            this.playlistDropped.emit({
              playlist: playlist,
              timeSlot: { hour, minute }
            });
          }
        } else {
          console.warn('No playlist data found in drag event');
        }
      } catch (error) {
        console.error('Error parsing dropped playlist data:', error);
      }
    }
  }
}